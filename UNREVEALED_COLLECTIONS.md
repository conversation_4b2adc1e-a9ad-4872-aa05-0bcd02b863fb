# Unrevealed Telegram Collections

This document describes the implementation of the Unrevealed Telegram Collections feature, which displays Telegram Star Gift collections that don't have upgrade options available yet.

## Overview

The feature fetches Telegram Star Gift collections using the `payments.getStarGifts` API method and filters them to show only collections where `can_upgrade` is `false`, `null`, or `undefined`. These are considered "unrevealed" collections because they haven't been enabled for upgrades yet.

## Files Created

### 1. `/src/api/telegram-api.ts`
- **Purpose**: API integration for Telegram Star Gifts
- **Key Functions**:
  - `getStarGifts()`: Fetches all star gifts from Telegram API
  - `getUnrevealedCollections()`: Filters gifts to show only non-upgradeable ones
- **Current State**: Uses mock data, ready for real API integration

### 2. `/src/app/unrevealed-collections/page.tsx`
- **Purpose**: React page component to display unrevealed collections
- **Features**:
  - Grid layout showing collection cards
  - Refresh functionality
  - Loading states
  - Empty state handling
  - Collection details (price, availability, convert value)

### 3. `/src/components/ui/badge.tsx`
- **Purpose**: UI component for status badges
- **Usage**: Shows collection status (Limited, Sold Out, Not Available)

## Data Structure

### StarGift Interface
```typescript
interface StarGift {
  id: string;
  title: string;
  emoji: string;
  stars: number;
  upgrade_stars?: number;
  count: number;
  total_count: number;
  can_upgrade?: boolean;  // Key field for filtering
  limited?: boolean;
  sold_out?: boolean;
  // ... other fields
}
```

## Integration with Real Telegram API

### Requirements
1. **Python Library**: Install `kurigram==2.2.4`
2. **Telegram Account**: Account with API access
3. **Authentication**: API ID, API Hash, and session management

### Implementation Steps

1. **Set up Telegram Client**:
```python
from kurigram import Client

async def get_star_gifts():
    async with Client("session_name", api_id, api_hash) as app:
        result = await app.invoke(
            raw.functions.payments.GetStarGifts(hash=0)
        )
        return result
```

2. **Create API Endpoint**: 
   - Add a Firebase Cloud Function or API route
   - Call the Python script from Node.js
   - Parse and return data in the expected format

3. **Update Frontend**:
   - Replace mock data in `getStarGifts()` function
   - Add error handling for API failures
   - Implement caching if needed

### Alternative: TypeScript Implementation

If you prefer a pure TypeScript solution, you can:
1. Use a Telegram MTProto library for Node.js
2. Implement the `payments.getStarGifts` method call
3. Handle authentication and session management

## API Response Format

The Telegram API returns data in this format:
```
{
  "gifts": [
    {
      "id": "gift_id",
      "title": "Gift Title", 
      "emoji": "🎁",
      "stars": 100,
      "can_upgrade": false,  // This is the key field
      // ... other fields
    }
  ],
  "hash": 123456789
}
```

## Filtering Logic

Collections are considered "unrevealed" when:
- `can_upgrade === false`
- `can_upgrade === null` 
- `can_upgrade === undefined`

This indicates that Telegram hasn't enabled upgrade functionality for these collections yet.

## UI Features

### Collection Cards Display:
- Collection emoji and title
- Star price and convert value
- Availability count
- Status badges (Limited, Sold Out)
- Upgrade status indicator

### Page Features:
- Loading states
- Refresh button
- Empty state message
- Responsive grid layout
- Error handling with toast notifications

## Navigation

The page is accessible from the main navigation at `/unrevealed-collections` and has been added to the home page grid.

## Future Enhancements

1. **Real-time Updates**: WebSocket connection for live data
2. **Filtering Options**: Filter by price, availability, etc.
3. **Sorting**: Sort by price, date, availability
4. **Export**: Export collection data to CSV/JSON
5. **Notifications**: Alert when collections become upgradeable
6. **Analytics**: Track which collections are most popular

## Testing

Currently uses mock data with 5 sample collections demonstrating different states:
- Collections with and without upgrades
- Limited and unlimited collections
- Available and sold out collections
- Various price points and availability counts

## Deployment Notes

1. Ensure Telegram API credentials are properly configured
2. Set up proper error handling and logging
3. Consider rate limiting for API calls
4. Implement caching to reduce API calls
5. Monitor API usage and costs

import { getTelegramBotToken } from "./config";

/**
 * Verify that the request is coming from the authorized Telegram bot
 * @param providedToken - The bot token provided in the request
 * @returns boolean - True if the token is valid
 */
export function verifyBotToken(providedToken: string): boolean {
  try {
    const expectedToken = getTelegramBotToken();
    
    if (!providedToken || !expectedToken) {
      return false;
    }

    // Use constant-time comparison to prevent timing attacks
    return constantTimeCompare(providedToken, expectedToken);
  } catch (error) {
    console.error("Error verifying bot token:", error);
    return false;
  }
}

/**
 * Constant-time string comparison to prevent timing attacks
 * @param a - First string
 * @param b - Second string
 * @returns boolean - True if strings are equal
 */
function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

/**
 * Extract bot token from Authorization header
 * @param authHeader - Authorization header value (e.g., "Bot TOKEN_HERE")
 * @returns string | null - The extracted token or null if invalid format
 */
export function extractBotTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader) {
    return null;
  }

  // Expected format: "Bot TOKEN_HERE"
  const parts = authHeader.split(" ");
  if (parts.length !== 2 || parts[0] !== "Bot") {
    return null;
  }

  return parts[1];
}

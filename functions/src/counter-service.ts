import * as admin from "firebase-admin";

export interface CounterEntity {
  id: string;
  value: number;
  updatedAt: admin.firestore.Timestamp;
}

/**
 * Get the next auto-incremented number for a specific counter
 * @param counterName - Name of the counter (e.g., 'order_number')
 * @returns Promise<number> - The next incremented value
 */
export async function getNextCounterValue(counterName: string): Promise<number> {
  const db = admin.firestore();
  const counterRef = db.collection("counters").doc(counterName);

  try {
    // Use a transaction to ensure atomic increment
    const nextValue = await db.runTransaction(async (transaction) => {
      const counterDoc = await transaction.get(counterRef);

      let currentValue = 0;
      if (counterDoc.exists) {
        const counterData = counterDoc.data() as CounterEntity;
        currentValue = counterData.value;
      }

      const nextValue = currentValue + 1;

      // Update or create the counter document
      transaction.set(
        counterRef,
        {
          id: counterName,
          value: nextValue,
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        },
        { merge: true }
      );

      return nextValue;
    });

    return nextValue;
  } catch (error) {
    console.error(`Error getting next counter value for ${counterName}:`, error);
    throw error;
  }
}

/**
 * Initialize a counter with a specific starting value
 * @param counterName - Name of the counter
 * @param startValue - Starting value (default: 0)
 */
export async function initializeCounter(
  counterName: string,
  startValue: number = 0
): Promise<void> {
  const db = admin.firestore();
  const counterRef = db.collection("counters").doc(counterName);

  try {
    const counterDoc = await counterRef.get();
    
    if (!counterDoc.exists) {
      await counterRef.set({
        id: counterName,
        value: startValue,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
      console.log(`Counter ${counterName} initialized with value ${startValue}`);
    } else {
      console.log(`Counter ${counterName} already exists`);
    }
  } catch (error) {
    console.error(`Error initializing counter ${counterName}:`, error);
    throw error;
  }
}

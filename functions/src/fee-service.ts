import * as admin from "firebase-admin";
import { AppConfig, UserEntity } from "./types";
import { updateUserBalance } from "./balance-service";

const APP_CONFIG_COLLECTION = "app_config";
const APP_CONFIG_DOC_ID = "fees";

export async function getAppConfig(): Promise<AppConfig | null> {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(APP_CONFIG_COLLECTION)
      .doc(APP_CONFIG_DOC_ID)
      .get();

    if (!doc.exists) {
      console.log("App config not found, using zero fees");
      return null;
    }

    return doc.data() as AppConfig;
  } catch (error) {
    console.error("Error getting app config:", error);
    throw error;
  }
}

export function calculateFeeAmount(amount: number, feeBps: number): number {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return (amount * feeBps) / 10000;
}

export async function getAdminUser(): Promise<UserEntity | null> {
  try {
    const db = admin.firestore();
    const adminQuery = await db
      .collection("users")
      .where("role", "==", "admin")
      .limit(1)
      .get();

    if (adminQuery.empty) {
      console.error("No admin user found");
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    console.error("Error finding admin user:", error);
    throw error;
  }
}

export async function applyFeeToAdmin(
  feeAmount: number,
  feeType: string
): Promise<void> {
  if (feeAmount <= 0) {
    return;
  }

  try {
    const adminUser = await getAdminUser();
    if (!adminUser) {
      console.error(`Cannot apply ${feeType} fee: no admin user found`);
      return;
    }

    await updateUserBalance(adminUser.id, feeAmount, 0);
    console.log(
      `Applied ${feeType} fee of ${feeAmount} TON to admin user ${adminUser.id}`
    );
  } catch (error) {
    console.error(`Error applying ${feeType} fee to admin:`, error);
    throw error;
  }
}

export async function applyDepositFee(
  userId: string,
  depositAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.depositFee) {
      return depositAmount;
    }

    const feeAmount = calculateFeeAmount(depositAmount, config.depositFee);
    if (feeAmount <= 0) {
      return depositAmount;
    }

    const netAmount = depositAmount - feeAmount;

    await applyFeeToAdmin(feeAmount, "deposit");

    console.log(
      `Deposit fee applied: ${feeAmount} TON (${config.depositFee} BPS), net amount: ${netAmount} TON`
    );

    return netAmount;
  } catch (error) {
    console.error("Error applying deposit fee:", error);
    throw error;
  }
}

export async function applyRejectOrderFee(
  userId: string,
  orderAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.reject_order_fee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(orderAmount, config.reject_order_fee);
    if (feeAmount <= 0) {
      return 0;
    }

    // Deduct fee from user's balance
    await updateUserBalance(userId, -feeAmount, 0);

    // Apply fee to admin account
    await applyFeeToAdmin(feeAmount, "reject_order");

    console.log(
      `Reject order fee applied: ${feeAmount} TON (${config.reject_order_fee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying reject order fee:", error);
    throw error;
  }
}

export async function applyPurchaseFee(
  userId: string,
  purchaseAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.purchase_fee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(purchaseAmount, config.purchase_fee);
    if (feeAmount <= 0) {
      return 0;
    }

    await updateUserBalance(userId, -feeAmount, 0);

    await applyFeeToAdmin(feeAmount, "purchase");

    console.log(
      `Purchase fee applied: ${feeAmount} TON (${config.purchase_fee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying purchase fee:", error);
    throw error;
  }
}

export async function applyPurchaseFeeWithReferral(
  buyerId: string,
  purchaseAmount: number,
  referralId?: string
): Promise<{ totalFee: number; referralFee: number; marketplaceFee: number }> {
  try {
    const config = await getAppConfig();
    if (!config?.purchase_fee) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(
      purchaseAmount,
      config.purchase_fee
    );
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Deduct total fee from buyer
    await updateUserBalance(buyerId, -totalFeeAmount, 0);

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID and referrer fee is configured
    if (referralId && config.referrer_fee > 0) {
      referralFeeAmount = calculateFeeAmount(
        purchaseAmount,
        config.referrer_fee
      );
      marketplaceFeeAmount = totalFeeAmount - referralFeeAmount;

      // Find referrer by tg_id and give them the referral fee
      const db = admin.firestore();
      const referrerQuery = await db
        .collection("users")
        .where("tg_id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;

        // Add referral fee to referrer's balance
        await updateUserBalance(referrerId, referralFeeAmount, 0);

        console.log(
          `Referral fee applied: ${referralFeeAmount} TON (${config.referrer_fee} BPS) to referrer ${referrerId} (tg_id: ${referralId})`
        );
      } else {
        console.log(
          `Referrer with tg_id ${referralId} not found, adding full fee to marketplace`
        );
        // If referrer not found, add the referral fee back to marketplace fee
        marketplaceFeeAmount = totalFeeAmount;
        referralFeeAmount = 0;
      }
    }

    // Apply remaining fee to admin account
    if (marketplaceFeeAmount > 0) {
      await applyFeeToAdmin(marketplaceFeeAmount, "purchase");
    }

    console.log(
      `Purchase fee with referral applied: Total: ${totalFeeAmount} TON, Referral: ${referralFeeAmount} TON, Marketplace: ${marketplaceFeeAmount} TON`
    );

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    console.error("Error applying purchase fee with referral:", error);
    throw error;
  }
}

export async function applyWithdrawFee(
  userId: string,
  withdrawAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.withdrawFee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(withdrawAmount, config.withdrawFee);
    if (feeAmount <= 0) {
      return 0;
    }

    await applyFeeToAdmin(feeAmount, "withdrawal");

    console.log(
      `Withdrawal fee applied: ${feeAmount} TON (${config.withdrawFee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying withdrawal fee:", error);
    throw error;
  }
}

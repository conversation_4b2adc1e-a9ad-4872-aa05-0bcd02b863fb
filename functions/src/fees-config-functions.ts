import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { AppConfig } from "./types";

const APP_CONFIG_COLLECTION = "app_config";
const APP_CONFIG_DOC_ID = "fees";

export const updateFeesConfig = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  try {
    const db = admin.firestore();
    const userDoc = await db.collection("users").doc(context.auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError("not-found", "User not found.");
    }

    const userData = userDoc.data();
    if (userData?.role !== "admin") {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admin users can update fees configuration."
      );
    }

    // Validate the input data
    const {
      depositFee,
      withdrawFee,
      referrer_fee,
      reject_order_fee,
      purchase_fee,
    } = data;

    // Validate that all fees are numbers and within reasonable bounds (0-10000 BPS)
    const fees = {
      depositFee,
      withdrawFee,
      referrer_fee,
      reject_order_fee,
      purchase_fee,
    };

    for (const [key, value] of Object.entries(fees)) {
      if (typeof value !== "number" || value < 0 || value > 10000) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          `Invalid ${key}: must be a number between 0 and 10000 BPS`
        );
      }
    }

    // Validate that referrer_fee is not greater than purchase_fee
    if (referrer_fee > purchase_fee) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Referrer fee cannot be greater than purchase fee"
      );
    }

    // Update the fees configuration
    const feesConfig: AppConfig = {
      depositFee,
      withdrawFee,
      referrer_fee,
      reject_order_fee,
      purchase_fee,
    };

    await db
      .collection(APP_CONFIG_COLLECTION)
      .doc(APP_CONFIG_DOC_ID)
      .set(feesConfig, { merge: true });

    console.log("Fees configuration updated:", feesConfig);

    return {
      success: true,
      message: "Fees configuration updated successfully",
      config: feesConfig,
    };
  } catch (error) {
    console.error("Error in updateFeesConfig function:", error);
    
    // Re-throw HttpsError as-is
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while updating fees configuration."
    );
  }
});

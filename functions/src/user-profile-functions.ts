import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { UserEntity } from "./types";
import { prepareUserDataForSave } from "./utils";

export const changeUserData = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { name, tg_id, ton_wallet_address, referral_id } = data;
  const userId = context.auth.uid;

  try {
    const db = admin.firestore();
    
    // Get current user data
    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError("not-found", "User not found.");
    }

    const currentUserData = userDoc.data() as UserEntity;

    // Prepare update data
    const updateData: Partial<UserEntity> = {};

    // Update name if provided
    if (name !== undefined) {
      updateData.displayName = name;
    }

    // Update Telegram ID if provided
    if (tg_id !== undefined) {
      updateData.tg_id = tg_id;
    }

    // Update TON wallet address if provided
    if (ton_wallet_address !== undefined) {
      updateData.ton_wallet_address = ton_wallet_address;
    }

    // Update referral ID if provided and user doesn't already have one
    if (referral_id && !currentUserData.referral_id) {
      updateData.referral_id = referral_id;
      console.log(`Setting referral_id for user ${userId}: ${referral_id}`);
    } else if (referral_id && currentUserData.referral_id) {
      console.log(`User ${userId} already has referral_id: ${currentUserData.referral_id}, not updating`);
    }

    // Prepare data for save (handles TON address processing)
    const preparedData = prepareUserDataForSave(updateData);

    // Update user document
    await db.collection("users").doc(userId).update(preparedData);

    console.log(`User profile updated for ${userId}:`, preparedData);

    return {
      success: true,
      message: "User profile updated successfully",
      updatedFields: Object.keys(preparedData),
    };
  } catch (error) {
    console.error("Error in changeUserData function:", error);
    
    // Re-throw HttpsError as-is
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while updating user profile."
    );
  }
});

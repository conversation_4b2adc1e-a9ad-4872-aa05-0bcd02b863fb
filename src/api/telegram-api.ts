export interface StarGift {
  id: string;
  title: string;
  emoji: string;
  stars: number;
  upgrade_stars?: number;
  count: number;
  total_count: number;
  can_upgrade?: boolean;
  limited?: boolean;
  sold_out?: boolean;
  birthday?: boolean;
  sticker?: {
    id: string;
    access_hash: string;
    file_reference: string;
  };
  availability_remains?: number;
  availability_total?: number;
  convert_stars: number;
  first_sale_date?: number;
  last_sale_date?: number;
}

export interface StarGiftsResponse {
  gifts: StarGift[];
  hash: number;
}

// Mock data for development - replace with actual Telegram API call
const mockStarGifts: StarGift[] = [
  {
    id: "1",
    title: "Golden Star",
    emoji: "⭐",
    stars: 100,
    upgrade_stars: 200,
    count: 50,
    total_count: 100,
    can_upgrade: false,
    limited: true,
    sold_out: false,
    convert_stars: 80,
    availability_remains: 50,
    availability_total: 100,
  },
  {
    id: "2",
    title: "Diamond Gift",
    emoji: "💎",
    stars: 500,
    upgrade_stars: 1000,
    count: 25,
    total_count: 50,
    can_upgrade: null,
    limited: true,
    sold_out: false,
    convert_stars: 400,
    availability_remains: 25,
    availability_total: 50,
  },
  {
    id: "3",
    title: "Ruby Collection",
    emoji: "🔴",
    stars: 250,
    upgrade_stars: 500,
    count: 75,
    total_count: 100,
    can_upgrade: true,
    limited: false,
    sold_out: false,
    convert_stars: 200,
  },
  {
    id: "4",
    title: "Emerald Set",
    emoji: "🟢",
    stars: 300,
    count: 30,
    total_count: 60,
    can_upgrade: false,
    limited: true,
    sold_out: false,
    convert_stars: 240,
    availability_remains: 30,
    availability_total: 60,
  },
  {
    id: "5",
    title: "Sapphire Collection",
    emoji: "🔵",
    stars: 400,
    count: 40,
    total_count: 80,
    can_upgrade: null,
    limited: true,
    sold_out: true,
    convert_stars: 320,
    availability_remains: 0,
    availability_total: 80,
  },
];

/**
 * Fetches star gifts from Telegram API
 * In production, this should call the actual Telegram API using payments.getStarGifts
 *
 * To implement with real Telegram API:
 * 1. Install kurigram==2.2.4 (Python library) or equivalent TypeScript library
 * 2. Set up Telegram account authentication
 * 3. Call payments.getStarGifts method
 * 4. Parse the response to match StarGift interface
 *
 * Example with kurigram (Python):
 * ```python
 * from kurigram import Client
 *
 * async def get_star_gifts():
 *     async with Client("session_name", api_id, api_hash) as app:
 *         result = await app.invoke(
 *             raw.functions.payments.GetStarGifts(hash=0)
 *         )
 *         return result
 * ```
 */
export const getStarGifts = async (): Promise<StarGiftsResponse> => {
  try {
    // TODO: Replace with actual Telegram API call
    // const response = await telegramApi.call('payments.getStarGifts', { hash: 0 });

    // For now, return mock data
    await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API delay

    return {
      gifts: mockStarGifts,
      hash: Date.now(),
    };
  } catch (error) {
    console.error("Error fetching star gifts:", error);
    throw error;
  }
};

/**
 * Filters star gifts to only include those that cannot be upgraded yet
 * These are collections that don't have upgrades available
 */
export const getUnrevealedCollections = async (): Promise<StarGift[]> => {
  try {
    const response = await getStarGifts();

    console.log("response.gifts", response.gifts);

    // Filter gifts where can_upgrade is false or null (not upgradeable yet)
    const unrevealedGifts = response.gifts.filter(
      (gift) =>
        gift.can_upgrade === false ||
        gift.can_upgrade === null ||
        gift.can_upgrade === undefined
    );

    return unrevealedGifts;
  } catch (error) {
    console.error("Error fetching unrevealed collections:", error);
    throw error;
  }
};

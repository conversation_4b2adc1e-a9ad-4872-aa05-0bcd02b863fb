"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { httpsCallable } from "firebase/functions";

import { firebaseFunctions } from "@/root-context";
import { loadFeesConfig } from "@/api/fees-api";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2, DollarSign } from "lucide-react";

// Fee configuration schema
const feesSchema = z.object({
  depositFee: z.number().min(0).max(10000),
  withdrawFee: z.number().min(0).max(10000),
  referrer_fee: z.number().min(0).max(10000),
  reject_order_fee: z.number().min(0).max(10000),
  purchase_fee: z.number().min(0).max(10000),
});

type FeesFormData = z.infer<typeof feesSchema>;

export const FeesManagement = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const form = useForm<FeesFormData>({
    resolver: zodResolver(feesSchema),
    defaultValues: {
      depositFee: 100,
      withdrawFee: 50,
      referrer_fee: 200,
      reject_order_fee: 150,
      purchase_fee: 750,
    },
  });

  // Load current fees configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoadingData(true);
        const config = await loadFeesConfig();

        if (config) {
          form.reset({
            depositFee: config.depositFee || 100,
            withdrawFee: config.withdrawFee || 50,
            referrer_fee: config.referrer_fee || 200,
            reject_order_fee: config.reject_order_fee || 150,
            purchase_fee: config.purchase_fee || 750,
          });
        }
      } catch (error) {
        console.error("Error loading fees config:", error);
        toast({
          title: "Error",
          description: "Failed to load current fees configuration.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingData(false);
      }
    };

    loadConfig();
  }, [form, toast]);

  const onSubmit = async (data: FeesFormData) => {
    setIsLoading(true);

    try {
      // Call the Firebase callable function to update fees
      const updateFeesConfig = httpsCallable(
        firebaseFunctions,
        "updateFeesConfig"
      );

      await updateFeesConfig(data);

      toast({
        title: "Success",
        description: "Fees configuration has been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating fees config:", error);
      toast({
        title: "Error",
        description: "Failed to update fees configuration. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatBpsToPercent = (bps: number) => {
    return (bps / 100).toFixed(2);
  };

  if (isLoadingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Fees Management
          </CardTitle>
          <CardDescription>
            Configure marketplace fees and referral rates
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading fees configuration...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Fees Management
        </CardTitle>
        <CardDescription>
          Configure marketplace fees and referral rates. All fees are in basis
          points (BPS). 100 BPS = 1%.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="depositFee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Deposit Fee (BPS)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="100"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Fee applied to TON deposits &gt; 0.9 TON. Current:{" "}
                      {formatBpsToPercent(field.value)}%
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="withdrawFee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Withdraw Fee (BPS)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="50"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Fee applied to withdrawals. Current:{" "}
                      {formatBpsToPercent(field.value)}%
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="purchase_fee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purchase Fee (BPS)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="750"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Total fee applied to purchases. Current:{" "}
                      {formatBpsToPercent(field.value)}%
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="referrer_fee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Referrer Fee (BPS)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="200"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Part of purchase fee given to referrer. Current:{" "}
                      {formatBpsToPercent(field.value)}%
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reject_order_fee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Order Rejection Fee (BPS)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="150"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Fee applied when orders are rejected. Current:{" "}
                      {formatBpsToPercent(field.value)}%
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="bg-muted/50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">
                Fee Distribution Example (100 TON purchase):
              </h4>
              <div className="text-sm space-y-1">
                <div>
                  • Purchase Fee:{" "}
                  {formatBpsToPercent(form.watch("purchase_fee"))}% ={" "}
                  {((100 * form.watch("purchase_fee")) / 10000).toFixed(2)} TON
                </div>
                <div>
                  • Referrer Gets:{" "}
                  {formatBpsToPercent(form.watch("referrer_fee"))}% ={" "}
                  {((100 * form.watch("referrer_fee")) / 10000).toFixed(2)} TON
                </div>
                <div>
                  • Marketplace Gets:{" "}
                  {formatBpsToPercent(
                    form.watch("purchase_fee") - form.watch("referrer_fee")
                  )}
                  % ={" "}
                  {(
                    (100 *
                      (form.watch("purchase_fee") -
                        form.watch("referrer_fee"))) /
                    10000
                  ).toFixed(2)}{" "}
                  TON
                </div>
                <div>
                  • Seller Gets:{" "}
                  {(100 - (100 * form.watch("purchase_fee")) / 10000).toFixed(
                    2
                  )}{" "}
                  TON
                </div>
              </div>
            </div>

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Fees...
                </>
              ) : (
                "Update Fees Configuration"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

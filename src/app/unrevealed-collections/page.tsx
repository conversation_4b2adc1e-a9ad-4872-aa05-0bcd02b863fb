"use client";

import { useState, useEffect } from "react";
import { getUnrevealedCollections, StarGift } from "@/api/telegram-api";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Star, Gift, RefreshCw } from "lucide-react";

export default function UnrevealedCollectionsPage() {
  const { toast } = useToast();
  const [collections, setCollections] = useState<StarGift[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadCollections = async (showRefreshLoader = false) => {
    try {
      if (showRefreshLoader) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const unrevealedCollections = await getUnrevealedCollections();
      setCollections(unrevealedCollections);
    } catch (error) {
      console.error("Error loading unrevealed collections:", error);
      toast({
        title: "Error",
        description: "Failed to load unrevealed collections. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadCollections();
  }, []);

  const handleRefresh = () => {
    loadCollections(true);
  };

  const formatStars = (stars: number) => {
    return stars.toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5" />
                Unrevealed Telegram Collections
              </CardTitle>
              <CardDescription>
                Collections that don't have upgrades available yet
              </CardDescription>
            </CardHeader>
            <CardContent className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading collections...</span>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Gift className="h-8 w-8" />
              Unrevealed Telegram Collections
            </h1>
            <p className="text-muted-foreground mt-2">
              Star gift collections that don't have upgrades available yet
            </p>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            {isRefreshing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>

        {collections.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Gift className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Unrevealed Collections</h3>
              <p className="text-muted-foreground text-center">
                All available collections currently have upgrades available.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {collections.map((collection) => (
              <Card key={collection.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <span className="text-2xl">{collection.emoji}</span>
                      {collection.title}
                    </CardTitle>
                    {collection.limited && (
                      <Badge variant="secondary">Limited</Badge>
                    )}
                  </div>
                  {collection.sold_out && (
                    <Badge variant="destructive" className="w-fit">
                      Sold Out
                    </Badge>
                  )}
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Price</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-semibold">{formatStars(collection.stars)}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Available</span>
                    <span className="font-semibold">
                      {collection.availability_remains !== undefined
                        ? `${collection.availability_remains}/${collection.availability_total}`
                        : `${collection.count}/${collection.total_count}`}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Convert Value</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-semibold">{formatStars(collection.convert_stars)}</span>
                    </div>
                  </div>

                  {collection.upgrade_stars && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Upgrade Cost</span>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="font-semibold">{formatStars(collection.upgrade_stars)}</span>
                      </div>
                    </div>
                  )}

                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Upgrade Status</span>
                      <Badge variant="outline" className="text-orange-600 border-orange-600">
                        Not Available
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <div className="mt-8 p-4 bg-muted/50 rounded-lg">
          <h3 className="font-semibold mb-2">About Unrevealed Collections</h3>
          <p className="text-sm text-muted-foreground">
            These are Telegram Star Gift collections that don't have upgrade options available yet. 
            Collections become "revealed" when Telegram enables upgrade functionality for them. 
            This page helps track which collections are still in their initial state without upgrade paths.
          </p>
        </div>
      </div>
    </div>
  );
}
